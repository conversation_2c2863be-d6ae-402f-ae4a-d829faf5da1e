"""
用户信息服务

提供用户信息获取功能，遵循DDD架构模式
"""

import json
from typing import Optional, Dict, Any
from src.repositories.chatbi.user import UserRepository, ChatbiUserRepository
from src.utils.logger import logger
from src.utils.in_memory_cache import in_memory_cache
from src.utils.user_utils import get_api_token


class UserInfoService:
    """用户信息服务类"""
    
    def __init__(self, user_repository: Optional[UserRepository] = None):
        """
        初始化用户信息服务
        
        Args:
            user_repository: 用户仓储接口实现，如果为None则使用默认实现
        """
        self.user_repository = user_repository or ChatbiUserRepository()
    
    @in_memory_cache(expire_seconds=600)  # 缓存10分钟
    def get_user_info_by_open_id(self, open_id: str) -> Optional[Dict[str, Any]]:
        """
        根据open_id从数据库获取用户信息
        
        Args:
            open_id: 用户的open_id
            
        Returns:
            Optional[Dict[str, Any]]: 用户信息字典，未找到时返回None
        """
        try:
            user_info = self.user_repository.find_full_user_info_by_open_id(open_id)

            if not user_info:
                return None

            # 处理avatar字段：如果是JSON字符串则尝试反序列化，否则直接使用字符串URL
            avatar_data = user_info.get('avatar')
            if avatar_data and isinstance(avatar_data, str):
                # 先尝试作为JSON解析，如果失败则当作普通字符串URL处理
                try:
                    parsed_avatar = json.loads(avatar_data)
                    # 如果解析成功且是字典，提取avatar_thumb或其他字段
                    if isinstance(parsed_avatar, dict):
                        avatar_data = (parsed_avatar.get("avatar_thumb") or
                                     parsed_avatar.get("avatar_middle") or
                                     parsed_avatar.get("avatar_big") or
                                     parsed_avatar.get("avatar_origin"))
                    else:
                        avatar_data = parsed_avatar
                except (json.JSONDecodeError, TypeError):
                    # 如果JSON解析失败，说明是普通字符串URL，直接使用
                    pass

            # 将avatar处理后的数据更新到用户数据中
            user_info['avatar'] = avatar_data

            # 如果有union_id，获取summerfarm_api_token
            union_id = user_info.get('union_id')
            if union_id:
                api_token = get_api_token(union_id=union_id)
                user_info['summerfarm_api_token'] = api_token

            return user_info

        except Exception as e:
            logger.exception(f"根据open_id获取用户信息失败: {open_id}, error: {e}")
            return None


# 全局服务实例
user_info_service = UserInfoService()


@in_memory_cache(expire_seconds=600)  # 缓存10分钟
def get_user_info_by_open_id(open_id: str) -> Optional[Dict[str, Any]]:
    """
    根据open_id从数据库获取用户信息的便捷函数
    
    Args:
        open_id: 用户的open_id
        
    Returns:
        Optional[Dict[str, Any]]: 用户信息字典，未找到时返回None
    """
    return user_info_service.get_user_info_by_open_id(open_id)
